import { <PERSON>, <PERSON><PERSON>, Card, CardBody } from '@heroui/react';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  image: string;
  badge: string;
}

interface RecommendationCardProps {
  recommendations: Recommendation[];
  showViewAll?: boolean;
  currentStartIndex: number;
  visibleCards: number;
}

const RecommendationCard = ({
  recommendations,
  showViewAll = true,
  currentStartIndex,
  visibleCards
}: RecommendationCardProps) => {
  // Create extended array for smooth infinite scroll
  const extendedRecommendations = [
    ...recommendations,
    ...recommendations,
    ...recommendations
  ];

  // Calculate the offset for smooth scrolling
  const cardHeight = 115; // Height of each card
  const scrollOffset = -(currentStartIndex * cardHeight);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex-1 overflow-hidden w-full relative">
        <motion.div
          className="flex flex-col gap-0 w-full"
          animate={{
            y: scrollOffset
          }}
          transition={{
            duration: 0.8,
            ease: "easeInOut"
          }}
        >
          {extendedRecommendations.map((item, index) => (
            <motion.div
              key={`${item.title}-${index}`}
              className="w-full"
              style={{ height: `${cardHeight}px` }}
            >
                <Card
                  className="bg-white border-none shadow-none hover:shadow hover:border-2 hover:border-black transition-all duration-200 w-full"
                  isHoverable
                  isPressable
                >
                  <CardBody className="p-4">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex gap-4 flex-1">
                        <div className="relative flex-shrink-0">
                          <Image
                            src={item.image}
                            alt={item.title}
                            width={120}
                            height={95}
                            className="w-[120px] h-[95px] object-cover rounded-xl"
                          />
                          <Chip
                            className="absolute -bottom-2 right-2 font-medium bg-[#5A5A5A] text-white border-2 border-white text-[10px] rounded-full h-[16px]"
                            color="primary"
                            variant="flat"
                            size="sm"
                          >
                            {item.badge}
                          </Chip>
                        </div>
                        <div className="text-sm space-y-1 flex-1 min-w-0">
                          <p className="font-medium text-base text-[#1C1C1C]">
                            {item.title}
                          </p>
                          <p className="text-[#808080] text-xs">{item.duration}</p>
                          <p className="text-[#808080] text-xs">{item.location}</p>
                          <p className="text-xs text-[#808080]">{item.tags}</p>
                        </div>
                      </div>
                      <div className="flex-shrink-0 ml-4">
                        <Chip
                          color="primary"
                          variant="flat"
                          size="md"
                          className="font-semibold h-[24px]"
                        >
                          Details
                        </Chip>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
          ))}
        </motion.div>
      </div>

      {showViewAll && (
        <motion.div
          className="flex justify-end mt-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <Button
            variant="light"
            size="sm"
            color="primary"
            className="rounded-full font-semibold p-0 h-[24px]"
          >
            View All
          </Button>
        </motion.div>
      )}
    </div>
  );
};

export default RecommendationCard;
