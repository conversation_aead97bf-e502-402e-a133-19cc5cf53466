'use client';

import { BsSliders } from 'react-icons/bs';
import { useEffect, useState, useRef, useCallback } from 'react';

import RecommendationCard from './RecommendationCard';

const Recommendation = () => {
  const [visibleCards, setVisibleCards] = useState(3);
  const [currentStartIndex, setCurrentStartIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const recommendations = [
    {
      title: 'Town Browsing',
      duration: '4 nights / 5 days',
      location: 'Aspen',
      tags: 'Activities, Explore, Leisure, Family',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'badge',
    },
    {
      title: 'Mountain Retreat',
      duration: '3 nights / 4 days',
      location: 'Colorado',
      tags: 'Nature, Relax, Hiking',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Top Rated',
    },
    {
      title: 'City Lights',
      duration: '2 nights / 3 days',
      location: 'New York',
      tags: 'Urban, Nightlife, Shopping',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Hot',
    },
    {
      title: 'Beach Paradise',
      duration: '5 nights / 6 days',
      location: 'Hawaii',
      tags: 'Beach, Relaxation, Water Sports',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Popular',
    },
    {
      title: 'Cultural Journey',
      duration: '6 nights / 7 days',
      location: 'Japan',
      tags: 'Culture, History, Food',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'New',
    },
  ];

  const calculateVisibleCards = useCallback(() => {
    if (containerRef.current && headerRef.current) {
      const containerHeight = containerRef.current.clientHeight;
      const headerHeight = headerRef.current.clientHeight;
      const viewAllButtonHeight = 32; // Approximate height of View All button
      const padding = 16; // Top and bottom padding

      const availableHeight = containerHeight - headerHeight - viewAllButtonHeight - padding;
      const cardHeight = 115; // Approximate height of each card (95px image + padding)

      const maxCards = Math.floor(availableHeight / cardHeight);
      const newVisibleCards = Math.max(1, maxCards); // Ensure at least 1 card is visible

      setVisibleCards(prev => {
        if (prev !== newVisibleCards) {
          setCurrentStartIndex(0); // Reset to start when card count changes
          return newVisibleCards;
        }
        return prev;
      });
    }
  }, []);

  useEffect(() => {
    // Initial calculation
    calculateVisibleCards();

    // Debounced resize handler
    const handleResize = () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      resizeTimeoutRef.current = setTimeout(() => {
        calculateVisibleCards();
      }, 100); // Debounce resize events
    };

    window.addEventListener('resize', handleResize);

    // Also listen for orientation changes (mobile)
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [calculateVisibleCards]);

  // Auto-next functionality with hover pause
  useEffect(() => {
    if (recommendations.length <= visibleCards || isHovered) return;

    const interval = setInterval(() => {
      setCurrentStartIndex(prev => {
        const maxStartIndex = recommendations.length - visibleCards;
        return prev >= maxStartIndex ? 0 : prev + 1;
      });
    }, 4000); // Change cards every 4 seconds

    return () => clearInterval(interval);
  }, [visibleCards, recommendations.length, isHovered]);

  return (
    <div
      ref={containerRef}
      className="bg-white h-full px-4 py-2 rounded-xl flex flex-col"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div ref={headerRef} className="flex flex-row items-center justify-between">
        <div>
          <p className="text-[#1C1C1C] text-lg font-bold">Recommendation</p>
        </div>
        <div className="flex flex-row items-center gap-2">
          <BsSliders />
          <p className="text-sm font-medium">Filter</p>
        </div>
      </div>
      <div className="flex-1 mt-2 flex flex-col justify-between w-full">
        <RecommendationCard
          recommendations={recommendations}
          showViewAll={true}
          currentStartIndex={currentStartIndex}
          visibleCards={visibleCards}
        />
      </div>
    </div>
  );
};

export default Recommendation;
