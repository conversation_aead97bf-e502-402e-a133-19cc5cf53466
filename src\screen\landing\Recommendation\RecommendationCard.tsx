import { <PERSON>, <PERSON><PERSON>, Card, CardBody } from '@heroui/react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  image: string;
  badge: string;
}

interface RecommendationCardProps {
  recommendations: Recommendation[];
  visibleCards: number;
  currentStartIndex: number;
  showViewAll?: boolean;
  isTransitioning?: boolean;
}

const RecommendationCard = ({
  recommendations,
  visibleCards,
  currentStartIndex,
  showViewAll = true,
  isTransitioning = false
}: RecommendationCardProps) => {
  // Create infinite scroll by duplicating cards for seamless transition
  const getVisibleCards = () => {
    const cards = [];
    for (let i = 0; i < visibleCards + 1; i++) { // +1 for smooth transition
      const index = (currentStartIndex + i) % recommendations.length;
      cards.push({
        ...recommendations[index],
        uniqueKey: `${recommendations[index].title}-${currentStartIndex}-${i}`
      });
    }
    return cards;
  };

  const visibleRecommendations = getVisibleCards();

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-hidden relative">
        <AnimatePresence mode="wait">
          <motion.div
          key={currentStartIndex}
          className="flex flex-col gap-0"
          initial={{ y: 120, opacity: 0.8 }} // Height of one card + slight opacity
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -120, opacity: 0.8 }}
          transition={{
            duration: 0.7,
            ease: [0.4, 0.0, 0.2, 1], // Material Design easing
            opacity: { duration: 0.4 }
          }}
        >
          {visibleRecommendations.map((item, index) => (
            <motion.div
              key={item.uniqueKey}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.5,
                delay: index * 0.1,
                ease: "easeOut"
              }}
            >
              <Card
                className="bg-white border-none shadow-none hover:shadow hover:border-2 hover:border-black transition-all duration-200"
                isHoverable
                isPressable
              >
                <CardBody>
                  <div className="flex items-center justify-between rounded-xl cursor-pointer w-full">
                    <div className="flex gap-4">
                      <div className="relative">
                        <Image
                          src={item.image}
                          alt={item.title}
                          width={120}
                          height={95}
                          className="w-[120px] h-[95px] object-cover rounded-xl"
                        />
                        <Chip
                          className="absolute -bottom-2 right-2 font-medium bg-[#5A5A5A] text-white border-2 border-white text-[10px] rounded-full h-[16px]"
                          color="primary"
                          variant="flat"
                          size="sm"
                        >
                          {item.badge}
                        </Chip>
                      </div>
                      <div className="text-sm space-y-1">
                        <p className="font-medium text-base text-[#1C1C1C]">
                          {item.title}
                        </p>
                        <p className="text-[#808080] text-xs">{item.duration}</p>
                        <p className="text-[#808080]  text-xs">{item.location}</p>
                        <p className="text-xs text-[#808080] ">{item.tags}</p>
                      </div>
                    </div>
                    <div>
                      <Chip
                        color="primary"
                        variant="flat"
                        size="md"
                        className="font-semibold h-[24px]"
                      >
                        Details
                      </Chip>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
          </motion.div>
        </AnimatePresence>
      </div>

      {showViewAll && (
        <div className="flex justify-end mt-2">
          <Button
            variant="light"
            size="sm"
            color="primary"
            className="rounded-full font-semibold p-0 h-[24px]"
          >
            View All
          </Button>
        </div>
      )}
    </div>
  );
};

export default RecommendationCard;
