'use client';

import { BsSliders } from 'react-icons/bs';
import { useEffect, useState, useRef, useCallback } from 'react';

import RecommendationCard from './RecommendationCard';

const Recommendation = () => {
  const [visibleCards, setVisibleCards] = useState(3);
  const [currentStartIndex, setCurrentStartIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const recommendations = [
    {
      title: 'Town Browsing',
      duration: '4 nights / 5 days',
      location: 'Aspen',
      tags: 'Activities, Explore, Leisure, Family',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'badge',
    },
    {
      title: 'Mountain Retreat',
      duration: '3 nights / 4 days',
      location: 'Colorado',
      tags: 'Nature, Relax, Hiking',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Top Rated',
    },
    {
      title: 'City Lights',
      duration: '2 nights / 3 days',
      location: 'New York',
      tags: 'Urban, Nightlife, Shopping',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Hot',
    },
    {
      title: 'Beach Paradise',
      duration: '5 nights / 6 days',
      location: 'Hawaii',
      tags: 'Beach, Relaxation, Water Sports',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Popular',
    },
    {
      title: 'Cultural Journey',
      duration: '6 nights / 7 days',
      location: 'Japan',
      tags: 'Culture, History, Food',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'New',
    },
  ];

  const calculateVisibleCards = useCallback(() => {
    if (containerRef.current && headerRef.current) {
      const containerHeight = containerRef.current.clientHeight;
      const headerHeight = headerRef.current.clientHeight;
      const viewAllButtonHeight = 32;
      const padding = 24; // Increased padding for better spacing

      const availableHeight = containerHeight - headerHeight - viewAllButtonHeight - padding;
      const cardHeight = 120; // More accurate card height including gaps

      const maxCards = Math.floor(availableHeight / cardHeight);
      const newVisibleCards = Math.max(2, Math.min(maxCards, 4)); // Between 2-4 cards

      // Only update if significantly different to prevent constant recalculation
      setVisibleCards(prev => {
        if (Math.abs(prev - newVisibleCards) > 0) {
          return newVisibleCards;
        }
        return prev;
      });
    }
  }, []);

  useEffect(() => {
    // Initial calculation with delay to ensure DOM is ready
    const initialTimeout = setTimeout(() => {
      calculateVisibleCards();
    }, 100);

    // Debounced resize handler - only for significant changes
    const handleResize = () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      resizeTimeoutRef.current = setTimeout(() => {
        calculateVisibleCards();
      }, 500); // Longer debounce to prevent frequent recalculation
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      clearTimeout(initialTimeout);
    };
  }, [calculateVisibleCards]);

  // Auto-next functionality with hover pause and smooth infinite scroll
  useEffect(() => {
    if (recommendations.length <= 1 || isHovered) return;

    const interval = setInterval(() => {
      setIsTransitioning(true);

      setCurrentStartIndex(prev => {
        const nextIndex = (prev + 1) % recommendations.length; // Infinite loop

        // Reset transition state after animation
        setTimeout(() => setIsTransitioning(false), 500);

        return nextIndex;
      });
    }, 4000); // Slower transition for better UX

    return () => clearInterval(interval);
  }, [recommendations.length, isHovered]);

  return (
    <div
      ref={containerRef}
      className="bg-white h-full px-4 py-2 rounded-xl flex flex-col"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div ref={headerRef} className="flex flex-row items-center justify-between">
        <div>
          <p className="text-[#1C1C1C] text-lg font-bold">Recommendation</p>
        </div>
        <div className="flex flex-row items-center gap-2">
          <BsSliders />
          <p className="text-sm font-medium">Filter</p>
        </div>
      </div>
      <div className="flex-1 mt-2 flex flex-col overflow-hidden">
        <RecommendationCard
          recommendations={recommendations}
          visibleCards={visibleCards}
          currentStartIndex={currentStartIndex}
          showViewAll={true}
          isTransitioning={isTransitioning}
        />
      </div>
    </div>
  );
};

export default Recommendation;
