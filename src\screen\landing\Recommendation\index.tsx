'use client';

import { BsSliders } from 'react-icons/bs';
import { useEffect, useState, useRef } from 'react';

import RecommendationCard from './RecommendationCard';

const Recommendation = () => {
  const [visibleCards, setVisibleCards] = useState(3);
  const [currentStartIndex, setCurrentStartIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  const recommendations = [
    {
      title: 'Town Browsing',
      duration: '4 nights / 5 days',
      location: 'Aspen',
      tags: 'Activities, Explore, Leisure, Family',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'badge',
    },
    {
      title: 'Mountain Retreat',
      duration: '3 nights / 4 days',
      location: 'Colorado',
      tags: 'Nature, Relax, Hiking',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Top Rated',
    },
    {
      title: 'City Lights',
      duration: '2 nights / 3 days',
      location: 'New York',
      tags: 'Urban, Nightlife, Shopping',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Hot',
    },
    {
      title: 'Beach Paradise',
      duration: '5 nights / 6 days',
      location: 'Hawaii',
      tags: 'Beach, Relaxation, Water Sports',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'Popular',
    },
    {
      title: 'Cultural Journey',
      duration: '6 nights / 7 days',
      location: 'Japan',
      tags: 'Culture, History, Food',
      image: 'https://heroui.com/images/album-cover.png',
      badge: 'New',
    },
  ];

  useEffect(() => {
    const calculateVisibleCards = () => {
      if (containerRef.current && headerRef.current) {
        const containerHeight = containerRef.current.clientHeight;
        const headerHeight = headerRef.current.clientHeight;
        const viewAllButtonHeight = 32; // Approximate height of View All button
        const padding = 16; // Top and bottom padding

        const availableHeight = containerHeight - headerHeight - viewAllButtonHeight - padding;
        const cardHeight = 115; // Approximate height of each card (95px image + padding)

        const maxCards = Math.floor(availableHeight / cardHeight);
        setVisibleCards(Math.max(1, maxCards)); // Ensure at least 1 card is visible
      }
    };

    calculateVisibleCards();
    window.addEventListener('resize', calculateVisibleCards);

    return () => window.removeEventListener('resize', calculateVisibleCards);
  }, []);

  // Auto-next functionality
  useEffect(() => {
    if (recommendations.length <= visibleCards) return;

    const interval = setInterval(() => {
      setCurrentStartIndex(prev => {
        const maxStartIndex = recommendations.length - visibleCards;
        return prev >= maxStartIndex ? 0 : prev + 1;
      });
    }, 3000); // Change cards every 3 seconds

    return () => clearInterval(interval);
  }, [visibleCards, recommendations.length]);

  const displayedRecommendations = recommendations.slice(
    currentStartIndex,
    currentStartIndex + visibleCards
  );

  return (
    <div ref={containerRef} className="bg-white h-full px-4 py-2 rounded-xl flex flex-col">
      <div ref={headerRef} className="flex flex-row items-center justify-between">
        <div>
          <p className="text-[#1C1C1C] text-lg font-bold">Recommendation</p>
        </div>
        <div className="flex flex-row items-center gap-2">
          <BsSliders />
          <p className="text-sm font-medium">Filter</p>
        </div>
      </div>
      <div className="flex-1 mt-2 flex flex-col justify-between">
        <RecommendationCard
          recommendations={displayedRecommendations}
          showViewAll={true}
        />
      </div>
    </div>
  );
};

export default Recommendation;
